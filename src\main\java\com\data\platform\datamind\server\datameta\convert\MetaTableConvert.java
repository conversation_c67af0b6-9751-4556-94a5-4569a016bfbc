package com.data.platform.datamind.server.datameta.convert;

import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTableCreateReqVO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTableRespVO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTableUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 表元数据 Convert
 *
 */
@Mapper
public interface MetaTableConvert {

    MetaTableConvert INSTANCE = Mappers.getMapper(MetaTableConvert.class);

    MetaTableDO convert(MetaTableCreateReqVO bean);

    MetaTableDO convert(MetaTableUpdateReqVO bean);

    MetaTableRespVO convert(MetaTableDO bean);

    List<MetaTableRespVO> convertList(List<MetaTableDO> list);

    PageResult<MetaTableRespVO> convertPage(PageResult<MetaTableDO> page);

}
